import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/services/permission_service.dart';
import 'package:luxury_app/features/news/news_item.dart';
import 'package:luxury_app/features/news/news_provider.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/utils/date_formatter.dart';
import 'package:luxury_app/shared/widgets/markdown_renderer.dart';
import 'package:luxury_app/shared/widgets/state_widgets.dart';

/// Виджет для отображения новостей
class NewsContent extends ConsumerStatefulWidget {
  const NewsContent({super.key});

  @override
  ConsumerState<NewsContent> createState() => _NewsContentState();
}

class _NewsContentState extends ConsumerState<NewsContent> {
  @override
  Widget build(BuildContext context) {
    final newsState = ref.watch(newsProvider);
    final permissionService = ref.watch(permissionServiceProvider);

    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Кнопка добавления новости для пользователей с правами
              if (permissionService.hasPermission(Permissions.canCreateNews))
                Padding(
                  padding: const EdgeInsets.only(bottom: AppSizes.paddingM),
                  child: ElevatedButton.icon(
                    onPressed: () => _showCreateNewsDialog(context),
                    icon: const Icon(LucideIcons.plus),
                    label: const Text('Добавить новость'),
                  ),
                ),

              DataStateWidget<List<NewsItem>>(
                isLoading: newsState.isLoading,
                error: newsState.error,
                data: newsState.data,
                onRetry: () => ref.read(newsProvider.notifier).refreshNews(),
                emptyMessage: 'Нет новостей',
                dataBuilder:
                    (newsList) =>
                        _buildNewsList(context, newsList, newsState.isLoading),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNewsList(
    BuildContext context,
    List<NewsItem> newsList,
    bool isLoading,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isLoading) const LinearProgressIndicator(),
        const SizedBox(height: 16),
        for (int i = 0; i < newsList.length; i++) ...[
          _buildNewsItem(context, newsList[i]),
          if (i < newsList.length - 1) const Divider(height: 48),
        ],
      ],
    );
  }

  Widget _buildNewsItem(BuildContext context, NewsItem newsItem) {
    final permissionService = ref.watch(permissionServiceProvider);
    final canEdit = permissionService.hasPermission(Permissions.canEditNews);
    final canDelete = permissionService.hasPermission(
      Permissions.canDeleteNews,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: MarkdownRenderer(
                data: newsItem.content,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
              ),
            ),
            // Кнопки управления новостью
            if (canEdit || canDelete)
              Padding(
                padding: const EdgeInsets.only(left: AppSizes.paddingS),
                child: Column(
                  children: [
                    if (canEdit)
                      IconButton(
                        onPressed: () => _showEditNewsDialog(context, newsItem),
                        icon: const Icon(LucideIcons.edit, size: 16),
                        tooltip: 'Редактировать',
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                      ),
                    if (canDelete)
                      IconButton(
                        onPressed:
                            () => _showDeleteNewsDialog(context, newsItem),
                        icon: const Icon(LucideIcons.trash, size: 16),
                        tooltip: 'Удалить',
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                      ),
                  ],
                ),
              ),
          ],
        ),
        Align(
          alignment: Alignment.centerRight,
          child: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              DateFormatter.formatDateTime(newsItem.createdAt),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withAlpha(150),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showCreateNewsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => _NewsEditDialog(
            title: 'Создать новость',
            initialContent: '',
            onSave: (content) async {
              try {
                await ref.read(newsProvider.notifier).createNews(content);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Новость создана'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Ошибка создания новости: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
    );
  }

  void _showEditNewsDialog(BuildContext context, NewsItem newsItem) {
    showDialog(
      context: context,
      builder:
          (context) => _NewsEditDialog(
            title: 'Редактировать новость',
            initialContent: newsItem.content,
            onSave: (content) async {
              try {
                await ref
                    .read(newsProvider.notifier)
                    .updateNews(newsItem.id, content);
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Новость обновлена'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Ошибка обновления новости: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
    );
  }

  void _showDeleteNewsDialog(BuildContext context, NewsItem newsItem) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Удалить новость?'),
            content: const Text('Это действие нельзя отменить.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Отмена'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  try {
                    await ref
                        .read(newsProvider.notifier)
                        .deleteNews(newsItem.id);
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Новость удалена'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Ошибка удаления новости: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                child: const Text('Удалить'),
              ),
            ],
          ),
    );
  }
}

/// Диалог для создания/редактирования новости
class _NewsEditDialog extends StatefulWidget {
  final String title;
  final String initialContent;
  final Future<void> Function(String content) onSave;

  const _NewsEditDialog({
    required this.title,
    required this.initialContent,
    required this.onSave,
  });

  @override
  State<_NewsEditDialog> createState() => _NewsEditDialogState();
}

class _NewsEditDialogState extends State<_NewsEditDialog> {
  late final TextEditingController _controller;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialContent);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Theme.of(context).colorScheme.surface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 420,
          maxHeight: MediaQuery.of(context).size.height * 0.65,
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Tooltip(
                    message: 'Markdown поддерживается',
                    child: Icon(
                      Icons.info_outline,
                      size: 18,
                      color: Colors.grey.shade400,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 18),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.surfaceContainerHighest.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: TextField(
                    controller: _controller,
                    maxLines: null,
                    expands: true,
                    style: Theme.of(context).textTheme.bodyMedium,
                    decoration: const InputDecoration(
                      hintText: 'Текст новости... (Markdown)',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 14,
                        vertical: 10,
                      ),
                    ),
                    textAlignVertical: TextAlignVertical.top,
                  ),
                ),
              ),
              const SizedBox(height: 22),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed:
                        _isSaving ? null : () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.7),
                      textStyle: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    child: const Text('Отмена'),
                  ),
                  const SizedBox(width: 8),
                  FilledButton(
                    onPressed: _isSaving ? null : _save,
                    style: FilledButton.styleFrom(
                      minimumSize: const Size(80, 40),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child:
                        _isSaving
                            ? const SizedBox(
                              width: 18,
                              height: 18,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Text('Сохранить'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _save() async {
    final content = _controller.text.trim();
    if (content.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Содержимое новости не может быть пустым'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      await widget.onSave(content);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      // Ошибка обрабатывается в родительском виджете
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
