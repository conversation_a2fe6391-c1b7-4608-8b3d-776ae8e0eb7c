import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:luxury_app/app_provider.dart';
import 'package:luxury_app/app_shell.dart';
import 'package:luxury_app/app_state.dart';
import 'package:luxury_app/features/ai_chat/ai_chat_view/ai_chat_screen.dart';
import 'package:luxury_app/features/auth/auth_screen.dart';
import 'package:luxury_app/features/news/news_content.dart';
import 'package:luxury_app/features/settings/auth/auth_models/user.dart';
import 'package:luxury_app/features/settings/auth/auth_provider.dart';
import 'package:luxury_app/features/settings/auth/view/reset_password_screen.dart';
import 'package:luxury_app/features/settings/settings_screen.dart';
import 'package:luxury_app/features/splash/splash_screen.dart';
import 'package:luxury_app/features/users/user_permissions_screen.dart';
import 'package:luxury_app/features/users/users_list_screen.dart';
import 'package:luxury_app/features/wiki/wiki_view/wiki_screen.dart';
import 'package:luxury_app/navigation/route_config.dart';

/// Провайдер для GoRouter, который реагирует на изменения авторизации
final goRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);

  return GoRouter(
    navigatorKey: AppRouter._rootNavigatorKey,
    initialLocation: '/splash',
    routes: AppRouter._createRoutes(),
    errorBuilder: (context, state) => const ErrorScreen(),
    redirect:
        (context, state) =>
            AppRouter._handleNavigation(context, state, authState),
    // Добавляем debugLogDiagnostics для отладки
    debugLogDiagnostics: kDebugMode,
  );
});

class AppRouter {
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();

  /// Получаем роутер из провайдера (deprecated - используйте goRouterProvider)
  @Deprecated('Используйте goRouterProvider вместо AppRouter.router')
  static GoRouter get router {
    throw UnsupportedError(
      'Используйте goRouterProvider вместо AppRouter.router',
    );
  }

  static String? _handleNavigation(
    BuildContext context,
    GoRouterState state,
    AuthState authState,
  ) {
    final currentLocation = state.matchedLocation;

    if (kDebugMode) {
      debugPrint('🔄 [AppRouter] Navigation check:');
      debugPrint('   Current location: $currentLocation');
      debugPrint('   Is authenticated: ${authState.isAuthenticated}');
      debugPrint('   Is loading: ${authState.isLoading}');
      debugPrint('   User: ${authState.user?.email ?? 'null'}');
    }

    // Если состояние авторизации ещё загружается, показываем SplashScreen
    if (authState.isLoading) {
      if (currentLocation != '/splash') {
        if (kDebugMode) {
          debugPrint('   ⏳ Auth loading, redirecting to /splash');
        }
        return '/splash';
      }
      return null;
    }
    // После загрузки, если мы на splash, делаем редирект
    if (!authState.isLoading && currentLocation == '/splash') {
      if (authState.isAuthenticated) {
        if (kDebugMode) {
          debugPrint('   ✅ Auth loaded, authenticated, redirecting to /news');
        }
        return '/news';
      } else {
        if (kDebugMode) {
          debugPrint(
            '   ✅ Auth loaded, not authenticated, redirecting to /auth',
          );
        }
        return '/auth';
      }
    }

    // Если пользователь не авторизован
    if (!authState.isAuthenticated) {
      // Разрешаем доступ только к экрану авторизации
      if (currentLocation != '/auth') {
        if (kDebugMode) {
          debugPrint('   🔒 Not authenticated, redirecting to /auth');
        }
        return '/auth';
      }
      if (kDebugMode) {
        debugPrint('   ✅ Already on auth screen, no redirect');
      }
      return null;
    }

    // Если пользователь авторизован
    if (authState.isAuthenticated) {
      // Если пользователь на экране авторизации, перенаправляем на главную
      if (currentLocation == '/auth') {
        if (kDebugMode) {
          debugPrint(
            '   🚀 Authenticated on auth screen, redirecting to /news',
          );
        }
        return '/news';
      }
      if (kDebugMode) {
        debugPrint('   ✅ Authenticated and on protected route, no redirect');
      }
      return null;
    }

    return null;
  }

  static List<RouteBase> _createRoutes() {
    return [
      GoRoute(
        path: '/splash',
        name: 'splash',
        pageBuilder:
            (context, state) => _buildPage(
              key: state.pageKey,
              child: const SplashScreen(),
              duration: Duration.zero,
            ),
      ),
      GoRoute(
        path: '/auth',
        name: 'auth',
        pageBuilder:
            (context, state) => _buildPage(
              key: state.pageKey,
              child: const AuthScreen(),
              duration: Duration.zero,
            ),
      ),
      ShellRoute(
        navigatorKey: GlobalKey<NavigatorState>(),
        builder: (context, state, child) => AppShell(body: child),
        routes: [
          GoRoute(
            path: RouteConfig.newsPath == '/' ? '/news' : RouteConfig.newsPath,
            name: RouteConfig.newsRoute,
            pageBuilder: (context, state) {
              _updateAppState(context, DrawerMode.news, screenTitle: 'Новости');
              return _buildPage(key: state.pageKey, child: const NewsContent());
            },
          ),
          GoRoute(
            path: RouteConfig.wikiPath,
            name: RouteConfig.wikiRoute,
            pageBuilder: (context, state) {
              final pageId = state.pathParameters['pageId']!;
              final fileName = state.uri.queryParameters['name'];

              // Обновляем состояние для wiki
              _updateAppState(
                context,
                DrawerMode.wiki,
                pageId: pageId,
                screenTitle: fileName ?? 'База знаний',
              );

              return _buildPage(
                key: state.pageKey,
                child: WikiContent(fileId: pageId, fileName: fileName),
              );
            },
          ),
          GoRoute(
            path: RouteConfig.aiPath,
            name: RouteConfig.aiRoute,
            pageBuilder: (context, state) {
              final chatIdString = state.pathParameters['chatId']!;
              final chatId = int.parse(chatIdString);

              // Обновляем состояние для AI чата
              _updateAppState(
                context,
                DrawerMode.chat,
                assistantId: chatIdString,
                screenTitle: 'ИИ-ассистент',
              );

              // Добавляем отладочный лог
              debugPrint('🏗️ [AppRouter] Создаем страницу для чата $chatId');

              return _buildPage(
                key: state.pageKey,
                child: AIChatContent(chatId: chatId),
              );
            },
          ),

          GoRoute(
            path: 'reset-password',
            name: 'resetPassword',
            pageBuilder: (context, state) {
              final token = state.uri.queryParameters['token'] ?? '';
              final email = state.uri.queryParameters['email'] ?? '';
              return _buildPage(
                key: state.pageKey,
                child: ResetPasswordScreen(token: token, email: email),
              );
            },
          ),
          GoRoute(
            path: RouteConfig.settingsPath,
            name: RouteConfig.settingsRoute,
            pageBuilder: (context, state) {
              // Обновляем состояние для настроек
              _updateAppState(
                context,
                DrawerMode.news,
                screenTitle: 'Настройки',
              );

              return _buildPage(
                key: state.pageKey,
                child: const SettingsScreen(),
              );
            },
          ),
          GoRoute(
            path: RouteConfig.usersPath,
            name: RouteConfig.usersRoute,
            pageBuilder: (context, state) {
              // Обновляем состояние для пользователей
              _updateAppState(
                context,
                DrawerMode.users,
                screenTitle: 'Пользователи',
              );

              return _buildPage(
                key: state.pageKey,
                child: const UsersListScreen(),
              );
            },
          ),
          GoRoute(
            path: RouteConfig.userPermissionsPath,
            name: RouteConfig.userPermissionsRoute,
            pageBuilder: (context, state) {
              final userId = state.pathParameters['userId']!;
              final user = state.extra as User?;

              // Если объект User не передан через extra, создаем базовую версию
              final userToUse =
                  user ??
                  User(
                    id: userId,
                    email: 'Загрузка...',
                    role: 'user',
                    createdAt: DateTime.now(),
                  );

              // Обновляем состояние для управления правами пользователя
              _updateAppState(
                context,
                DrawerMode.users,
                screenTitle: 'Права пользователя',
              );

              return _buildPage(
                key: state.pageKey,
                child: UserPermissionsScreen(user: userToUse),
              );
            },
          ),
        ],
      ),
    ];
  }

  /// Обновляет состояние приложения при переходе на новую страницу
  static void _updateAppState(
    BuildContext context,
    DrawerMode mode, {
    String? pageId,
    String? assistantId,
    String? screenTitle,
  }) {
    // Используем WidgetsBinding для отложенного выполнения
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final container = ProviderScope.containerOf(context);
        final notifier = container.read(appProvider.notifier);

        notifier.navigateToScreen(
          mode,
          pageId: pageId,
          assistantId: assistantId,
        );

        // Устанавливаем заголовок экрана, если он передан
        if (screenTitle != null) {
          notifier.setScreenTitle(screenTitle);
        }

        if (kDebugMode) {
          debugPrint(
            '🔄 [AppRouter] Обновили состояние: mode=$mode, pageId=$pageId, assistantId=$assistantId, title=$screenTitle',
          );
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint('❌ [AppRouter] Ошибка обновления состояния: $e');
        }
      }
    });
  }

  static Page<void> _buildPage({
    required LocalKey key,
    required Widget child,
    Duration? duration,
  }) {
    return CustomTransitionPage<void>(
      key: key,
      child: child,
      transitionDuration: duration ?? const Duration(milliseconds: 250),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(opacity: animation, child: child);
      },
    );
  }
}

class ErrorScreen extends StatelessWidget {
  const ErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Ошибка')),
      body: const Center(child: Text('Страница не найдена')),
    );
  }
}
